"""
Spekter游戏AI Spin任务 - 使用Gemini AI自动执行
让AI智能识别页面并执行Spin操作

增强功能:
- 支持多种API密钥配置
- 反检测人性化行为
- 重试机制和错误处理
- 详细的状态检查和截图
"""
import asyncio
import os
import datetime

async def run_stealth_task(browser_session, browser_num):
    """
    使用AI Agent执行Spekter游戏Spin任务
    
    Args:
        browser_session: browser-use的BrowserSession对象
        browser_num: 浏览器编号
    """
    try:
        # 检查依赖
        try:
            from browser_use import Agent
            # 尝试导入可用的LLM
            try:
                from langchain_google_genai import ChatGoogleGenerativeAI
                from pydantic import SecretStr
                llm_available = "langchain"
            except ImportError:
                try:
                    from browser_use.llm import ChatGoogle
                    llm_available = "browser_use"
                except ImportError:
                    print("错误: 无法导入任何可用的LLM库")
                    print("请安装: pip install langchain-google-genai 或 pip install browser-use[llm]")
                    return
        except ImportError as e:
            print(f"缺少browser-use依赖: {e}")
            print("请安装: pip install browser-use")
            return

        print(f"浏览器 {browser_num} 开始Spekter AI Spin任务")

        # 获取API密钥 - 兼容多种环境变量名
        from dotenv import load_dotenv
        load_dotenv()

        api_key = os.getenv('GEMINI_API_KEY') or os.getenv('GOOGLE_API_KEY')

        if not api_key:
            print("警告: 未找到API密钥环境变量")
            print("请在.env文件中设置GEMINI_API_KEY或GOOGLE_API_KEY")
            print("或设置环境变量:")
            print("Windows: set GOOGLE_API_KEY=your_api_key")
            print("Linux/Mac: export GOOGLE_API_KEY=your_api_key")
            return

        # 创建LLM实例
        if llm_available == "langchain":
            llm = ChatGoogleGenerativeAI(
                model="gemini-2.0-flash-exp",
                api_key=SecretStr(api_key),
                temperature=0.1
            )
            print(f"浏览器 {browser_num} 使用langchain-google-genai")
        else:
            llm = ChatGoogle(
                model="gemini-1.5-flash",
                api_key=api_key,
                temperature=0.1
            )
            print(f"浏览器 {browser_num} 使用browser-use LLM")
        
        # 定义Spekter游戏任务 - 增强版反检测
        task_description = """
        请执行以下Spekter游戏任务，并模拟真实用户行为：

        HUMAN-LIKE BEHAVIOR (重要 - 避免检测):
        - 像真实用户一样浏览，不要过于机械化
        - 在每个操作之间添加自然的停顿（2-5秒）
        - 偶尔移动鼠标到不同位置
        - 滚动页面查看内容，就像真实用户在阅读
        - 如果页面有加载动画，耐心等待完成

        TASK STEPS:
        1. 访问 https://dispatch.spekter.games/spin 网站
        2. 等待页面完全加载（至少3-5秒）
        3. 慢慢滚动页面，观察整体布局和内容
        4. 寻找连接钱包或登录的按钮（如果需要）
        5. 仔细寻找"Spin"、"Play"、"Start"或类似的游戏按钮
        6. 在点击前稍作停顿（2-3秒），模拟思考时间
        7. 点击Spin按钮开始游戏
        8. 耐心等待游戏动画和结果（30-60秒）
        9. 观察是否有奖励、积分或其他结果显示
        10. 处理任何弹窗、确认按钮或后续操作
        11. 如果游戏可以重复，可以再次尝试

        IMPORTANT NOTES:
        - 页面可能需要连接Web3钱包（MetaMask等）
        - Spin按钮可能有不同的样式、颜色或文字
        - 游戏可能有冷却时间或次数限制
        - 保持自然的浏览节奏，避免过快操作
        - 如果遇到错误或加载问题，等待后重试
        - 截图记录重要步骤和结果

        ERROR HANDLING:
        - 如果页面加载失败，等待10秒后刷新
        - 如果找不到Spin按钮，仔细检查页面所有区域
        - 如果需要登录或连接钱包，尝试寻找相关按钮
        - 如果游戏卡住，等待更长时间或刷新页面
        """
        
        print(f"浏览器 {browser_num} 创建AI Agent...")

        # 创建AI Agent - 增强配置
        agent = Agent(
            task=task_description,
            llm=llm,
            browser_session=browser_session,
            max_actions=35,  # 增加操作步数，应对复杂页面和人性化行为
            use_vision=True,  # 启用视觉理解，帮助识别按钮
            # 添加更多配置以提高成功率
        )

        print(f"浏览器 {browser_num} AI Agent开始执行Spekter任务...")

        # 执行任务 - 添加重试机制
        max_retries = 2
        retry_count = 0

        while retry_count <= max_retries:
            try:
                print(f"浏览器 {browser_num} 尝试执行任务 (第{retry_count + 1}次)")
                result = await agent.run()

                print(f"浏览器 {browser_num} AI Agent任务执行完成")
                print(f"执行结果: {result}")
                break  # 成功执行，退出重试循环

            except Exception as e:
                retry_count += 1
                print(f"浏览器 {browser_num} 任务执行失败 (第{retry_count}次): {str(e)}")

                if retry_count <= max_retries:
                    print(f"浏览器 {browser_num} 将在10秒后重试...")
                    await asyncio.sleep(10)

                    # 尝试刷新页面
                    try:
                        page = await browser_session.get_current_page()
                        await page.reload()
                        await asyncio.sleep(5)  # 等待页面加载
                    except Exception as refresh_error:
                        print(f"页面刷新失败: {refresh_error}")
                else:
                    print(f"浏览器 {browser_num} 达到最大重试次数，任务失败")
                    raise e

        # 额外等待，确保游戏完成
        print(f"浏览器 {browser_num} 额外等待30秒确保游戏完成...")
        await asyncio.sleep(30)
        
        # 保存最终截图和状态检查
        try:
            page = await browser_session.get_current_page()

            # 保存最终截图
            import datetime
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            screenshot_path = f"screenshots/spekter_ai_final_browser_{browser_num}_{timestamp}.png"

            # 确保screenshots目录存在
            os.makedirs("screenshots", exist_ok=True)

            await page.screenshot(path=screenshot_path, full_page=True)
            print(f"浏览器 {browser_num} Spekter AI任务截图已保存: {screenshot_path}")

            # 检查页面状态
            try:
                page_title = await page.title()
                current_url = page.url
                print(f"浏览器 {browser_num} 最终状态:")
                print(f"  页面标题: {page_title}")
                print(f"  当前URL: {current_url}")

                # 尝试检查是否有成功指示器
                success_indicators = [
                    "success", "complete", "reward", "win", "congratulations",
                    "成功", "完成", "奖励", "获得"
                ]

                page_content = await page.content()
                found_indicators = [indicator for indicator in success_indicators
                                  if indicator.lower() in page_content.lower()]

                if found_indicators:
                    print(f"浏览器 {browser_num} 检测到可能的成功指示器: {found_indicators}")

            except Exception as status_error:
                print(f"状态检查失败: {status_error}")

        except Exception as e:
            print(f"保存截图失败: {e}")

        print(f"浏览器 {browser_num} Spekter AI Spin任务完成")

    except Exception as e:
        print(f"浏览器 {browser_num} Spekter AI任务失败: {e}")
        import traceback
        traceback.print_exc()

        # 失败时也尝试保存截图用于调试
        try:
            page = await browser_session.get_current_page()
            import datetime
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            error_screenshot = f"screenshots/spekter_ai_error_browser_{browser_num}_{timestamp}.png"
            os.makedirs("screenshots", exist_ok=True)
            await page.screenshot(path=error_screenshot, full_page=True)
            print(f"错误截图已保存: {error_screenshot}")
        except Exception as screenshot_error:
            print(f"保存错误截图失败: {screenshot_error}")

# 兼容性函数
async def run_automation_task(browser_session, browser_num):
    """兼容性函数"""
    await run_stealth_task(browser_session, browser_num)

# GPT-5评估提交脚本 - 反检测版本

## 概述

这个脚本已经被修改为具有强大的反检测能力，能够模拟真实用户行为来避免被网站检测为自动化脚本。

## 🛡️ 反检测特性

### 1. 人性化行为模拟
- **随机延迟**: 在每个操作之间添加随机的人性化延迟
- **自然鼠标移动**: 模拟真实用户的鼠标移动轨迹
- **页面滚动**: 像真实用户一样滚动页面阅读内容
- **思考时间**: 在填写表单前模拟思考和阅读时间

### 2. 智能内容生成
- **避免AI痕迹**: 生成更自然、人性化的内容
- **变化写作风格**: 每次提交使用不同的写作风格
- **领域专业知识**: 包含真实的专业知识和见解
- **轻微不完美**: 添加人类写作的自然特征

### 3. 行为随机化
- **操作顺序**: 随机化某些操作的顺序
- **打字模式**: 模拟不同的打字习惯和速度
- **检查行为**: 模拟用户检查和修改内容的行为
- **技能选择**: 智能轮换不同的技能类型

## 📁 文件结构

```
task/
├── orochi-sele.py              # 主脚本（已增强反检测）
├── anti_detection_config.py    # 反检测配置文件
├── test_evaluation_task.py     # 测试脚本
└── README_ANTI_DETECTION.md    # 本文档
```

## 🚀 使用方法

### 1. 环境准备
```bash
# 确保已安装必要依赖
pip install langchain-google-genai browser-use python-dotenv

# 设置API密钥
echo "GEMINI_API_KEY=your_api_key_here" > .env
```

### 2. 运行脚本
通过Chrome管理器运行：
1. 启动Chrome管理器
2. 选择 `orochi-sele.py` 任务
3. 脚本将自动执行反检测的评估提交

### 3. 监控执行
脚本会输出详细的执行日志，包括：
- 人性化行为的执行
- 内容生成过程
- 反检测措施的应用

## ⚙️ 配置选项

### 时间延迟配置
```python
DELAYS = {
    'page_load_wait': (3, 8),      # 页面加载等待
    'reading_time': (5, 15),       # 阅读时间
    'thinking_time': (2, 8),       # 思考时间
    'typing_pause': (0.1, 0.5),    # 打字间隔
    'submit_hesitation': (2, 5),   # 提交前犹豫
}
```

### 行为模式配置
```python
MOUSE_BEHAVIOR = {
    'natural_movement': True,       # 自然鼠标移动
    'random_clicks': True,          # 随机点击
    'hover_elements': True,         # 悬停元素
    'scroll_behavior': True,        # 滚动行为
}
```

## 🔒 安全特性

### 1. 检测规避
- **用户代理随机化**: 使用真实的浏览器用户代理
- **请求头模拟**: 模拟真实浏览器的请求头
- **行为模式变化**: 每次执行使用不同的行为模式
- **时间分布**: 避免过于规律的时间模式

### 2. 内容安全
- **原创性**: 确保每次提交的内容都是原创的
- **质量控制**: 生成高质量、有价值的评估内容
- **多样性**: 避免重复或相似的提交模式
- **专业性**: 体现真实的专业知识和见解

### 3. 错误处理
- **智能重试**: 遇到错误时使用更人性化的重试策略
- **状态检测**: 检测是否被标记为机器人并调整行为
- **优雅降级**: 在检测到风险时自动调整策略

## 📊 检测风险评估

### 低风险指标 ✅
- 自然的时间间隔
- 人性化的鼠标移动
- 真实的内容质量
- 变化的行为模式

### 中风险指标 ⚠️
- 过于完美的内容
- 规律的提交时间
- 缺少错误和修正

### 高风险指标 ❌
- 机械化的操作
- AI生成的明显痕迹
- 过快的执行速度
- 重复的内容模式

## 🛠️ 自定义配置

### 修改行为参数
编辑 `anti_detection_config.py` 文件来调整：
- 延迟时间范围
- 行为随机性
- 内容生成策略
- 技能选择策略

### 添加新的反检测措施
在配置文件中添加新的行为模式：
```python
# 添加新的打字模式
TYPING_PATTERNS = {
    'creative': "Type with bursts of inspiration",
    'analytical': "Type methodically with pauses",
    'casual': "Type conversationally with corrections"
}
```

## 📈 效果监控

### 成功指标
- 提交成功率 > 95%
- 无机器人检测警告
- 内容被接受率高
- 账户状态正常

### 监控建议
- 定期检查提交历史
- 监控账户状态变化
- 观察网站反馈
- 调整策略参数

## ⚠️ 注意事项

1. **合规使用**: 确保使用符合网站服务条款
2. **适度频率**: 不要过于频繁地提交
3. **质量优先**: 始终提交高质量的内容
4. **监控调整**: 根据效果调整反检测策略
5. **备份策略**: 准备多种不同的行为模式

## 🔄 更新维护

### 定期更新
- 监控网站变化
- 更新反检测策略
- 优化行为模式
- 改进内容质量

### 版本控制
- 保留工作版本的备份
- 记录重要的配置变更
- 测试新的反检测措施
- 维护配置文档

---

**免责声明**: 此脚本仅用于教育和研究目的。使用时请确保遵守相关网站的服务条款和法律法规。

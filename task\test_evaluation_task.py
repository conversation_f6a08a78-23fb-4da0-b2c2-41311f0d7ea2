#!/usr/bin/env python3
"""
测试GPT-5评估提交任务的脚本
用于验证orochi-sele.py的修改是否正确
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_import():
    """测试导入是否正常"""
    try:
        # 直接导入orochi-sele.py文件
        import importlib.util
        spec = importlib.util.spec_from_file_location("orochi_sele", "task/orochi-sele.py")
        orochi_sele = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(orochi_sele)
        print("✓ 导入成功")
        return True, orochi_sele
    except Exception as e:
        print(f"✗ 导入失败: {e}")
        return False, None

def test_configuration():
    """测试配置验证"""
    try:
        success, module = test_import()
        if not success:
            return False

        result = module.validate_configuration()
        if result:
            print("✓ 配置验证通过")
        else:
            print("✗ 配置验证失败 - 请检查GEMINI_API_KEY环境变量")
        return result
    except Exception as e:
        print(f"✗ 配置验证出错: {e}")
        return False

def test_function_exists():
    """测试必要的函数是否存在"""
    try:
        success, orochi_sele = test_import()
        if not success:
            return False
        
        functions = [
            'run_evaluation_task',
            'run_task', 
            'run_mining_task',
            'stop_task',
            'validate_configuration'
        ]
        
        missing_functions = []
        for func_name in functions:
            if not hasattr(orochi_sele, func_name):
                missing_functions.append(func_name)
        
        if missing_functions:
            print(f"✗ 缺少函数: {missing_functions}")
            return False
        else:
            print("✓ 所有必要函数都存在")
            return True
            
    except Exception as e:
        print(f"✗ 函数检查出错: {e}")
        return False

def main():
    """主测试函数"""
    print("开始测试GPT-5评估提交任务...")
    print("=" * 50)
    
    tests = [
        ("导入测试", lambda: test_import()[0]),
        ("函数存在性测试", test_function_exists),
        ("配置验证测试", test_configuration),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n运行 {test_name}...")
        if test_func():
            passed += 1
        
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("✓ 所有测试通过！orochi-sele.py修改成功")
        print("\n使用说明:")
        print("1. 确保已设置GEMINI_API_KEY环境变量")
        print("2. 通过Chrome管理器运行此任务")
        print("3. 任务将自动访问https://predict.recall.network/evaluations")
        print("4. AI将自动填写并提交评估表单")
    else:
        print("✗ 部分测试失败，请检查配置和代码")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

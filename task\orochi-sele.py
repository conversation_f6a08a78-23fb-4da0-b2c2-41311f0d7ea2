from langchain_google_genai import ChatGoogleGenerativeAI
from browser_use import Agent, <PERSON><PERSON><PERSON>, BrowserConfig
from pydantic import SecretStr
from dotenv import load_dotenv
import os
import asyncio
import logging
import time

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def run_mining_task(driver, task_running=True):
    """执行游戏挖矿砍树任务"""
    try:
        # 打印调试信息
        print(f"开始执行游戏挖矿砍树任务...")
        print(f"WebDriver类型: {type(driver).__name__}")
        if hasattr(driver, 'capabilities'):
            print(f"WebDriver能力: {driver.capabilities.get('browserName', '未知')}, 版本: {driver.capabilities.get('browserVersion', '未知')}")
        
        # 初始化 AI 和浏览器
        load_dotenv()
        api_key = os.getenv('GEMINI_API_KEY')
        llm = ChatGoogleGenerativeAI(
            model='gemini-2.0-flash-exp', 
            api_key=SecretStr(api_key)
        )

        print("获取调试地址...")
        debugger_address = driver.capabilities['goog:chromeOptions']['debuggerAddress']
        print(f"调试地址: {debugger_address}")
        browser = Browser(config=BrowserConfig(cdp_url=f"http://{debugger_address}"))

        # 创建代理
        print("创建AI代理...")
        agent = Agent(
            task="""
            GAME MINING AND CHOPPING TASK:
            
            1. Movement:
               - CRITICAL: To move in the game, you MUST perform the following steps:
                  1. PRESS DOWN and HOLD the movement key (don't release immediately)
                  2. Keep the key pressed for at least 5-10 seconds continuously
                  3. Only release the key when you want to stop moving
               
               - W: Press and HOLD to move forward (most important)
               - S: Press and HOLD to move backward
               - A: Press and HOLD to move left
               - D: Press and HOLD to move right
               
               - Try different movement durations: hold for 5 seconds, then 10 seconds
               - If one movement key doesn't work, try a different one (try W, then A, then D, then S)
               - If no movement occurs after trying all keys, try clicking on the game window first, then try movement again
            
            2. Resource detection:
               - If the character is not moving at all when holding WASD, focus first on getting ANY movement
               - Try holding W for a full 10 seconds without interruption
               - Once movement works, look for trees and mineral deposits while moving
               - Approach any visible resources by holding the appropriate movement key
            
            3. Tree chopping:
               a) When near a tree:
                  - RELEASE all movement keys completely
                  - Press 1 to switch to axe/weapon
                  - Watch for "Press F to chop" prompt to appear on screen
                  - When you see the prompt, repeatedly press F until the prompt disappears
                  - STOP pressing F immediately when the prompt disappears
                  - Resume movement by pressing and HOLDING a movement key (W/A/S/D) for at least 5 seconds
               
            4. Mining:
               a) When near an ore/mineral:
                  - RELEASE all movement keys completely
                  - Press 2 to switch to pickaxe
                  - Watch for "Press F to mine" prompt to appear on screen
                  - When you see the prompt, repeatedly press F until the prompt disappears
                  - STOP pressing F immediately when the prompt disappears
                  - Resume movement by pressing and HOLDING a movement key for at least 5 seconds
            
            5. Testing movement:
               - If character doesn't move when holding keys, try these troubleshooting steps:
                  1. Click on the game window to ensure it has focus
                  2. Try holding W key for 10-15 seconds without interruption
                  3. Try each movement key (W,A,S,D) one at a time, holding each for 10 seconds
                  4. Try pressing Escape once, then try movement again
                  5. Try movement after successfully pressing F for an action
            
            Important rules:
            - Movement requires HOLDING keys down continuously, not tapping them
            - Hold movement keys for at least 5-10 seconds at a time
            - Try all movement keys if one isn't working
            - Make sure the game window has focus before trying movement
            - Prioritize getting movement working first, then focus on resources
            - Always release movement keys before pressing 1, 2, or F
            """,
            llm=llm,
            browser=browser
        )

        # 错误计数和最大重试次数
        error_count = 0
        max_retries = 3
        
        # 运行任务
        while task_running:
            try:
                logger.info("开始执行挖矿砍树任务...")
                asyncio.run(agent.run())
                # 如果成功执行，重置错误计数
                error_count = 0
                
            except KeyboardInterrupt:
                logger.info("用户中断任务执行")
                return False
                
            except Exception as e:
                error_count += 1
                logger.error(f"任务执行出错 ({error_count}/{max_retries}): {str(e)}")
                
                if error_count >= max_retries:
                    logger.error(f"连续出错达到{max_retries}次，任务自动停止")
                    return False
                
                # 暂停一段时间后重试
                logger.info(f"将在5秒后重试...")
                time.sleep(5)
            
            # 检查任务状态
            if not task_running:
                logger.info("任务已被设置为停止状态，退出循环")
                break

        return True

    except Exception as e:
        logger.error(f"任务执行失败: {str(e)}")
        return False

# 为了保持向后兼容性，保留原始函数名
def run_task(driver, task_running=True):
    """兼容性函数 - 调用新的挖矿砍树任务"""
    return run_mining_task(driver, task_running)

def stop_task():
    """停止当前正在执行的任务"""
    logger.info("准备停止任务...")
    return False  # 返回False将使task_running变为False

if __name__ == "__main__":
    print("此脚本需要通过 Chrome 管理器运行")
    print("如需停止任务，请按Ctrl+C")
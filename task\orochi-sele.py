﻿from langchain_google_genai import ChatGoogleGenerativeAI
from browser_use import Agent, <PERSON>rowser, BrowserConfig
from pydantic import SecretStr
from dotenv import load_dotenv
import os
import asyncio
import logging
import time

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def run_evaluation_task(driver, task_running=True):
    """执行GPT-5评估提交任务"""
    try:
        # 打印调试信息
        print(f"开始执行GPT-5评估提交任务...")
        print(f"WebDriver类型: {type(driver).__name__}")
        if hasattr(driver, 'capabilities'):
            print(f"WebDriver能力: {driver.capabilities.get('browserName', '未知')}, 版本: {driver.capabilities.get('browserVersion', '未知')}")

        # 初始化 AI 和浏览器
        load_dotenv()
        # 尝试多个可能的API密钥环境变量名
        api_key = os.getenv('GEMINI_API_KEY') or os.getenv('GOOGLE_API_KEY')

        if not api_key:
            logger.error("未找到API密钥环境变量")
            logger.error("请在.env文件中设置GEMINI_API_KEY或GOOGLE_API_KEY")
            return False

        llm = ChatGoogleGenerativeAI(
            model='gemini-2.0-flash-exp',
            api_key=SecretStr(api_key)
        )

        print("获取调试地址...")
        debugger_address = driver.capabilities['goog:chromeOptions']['debuggerAddress']
        print(f"调试地址: {debugger_address}")
        browser = Browser(config=BrowserConfig(cdp_url=f"http://{debugger_address}"))

def run_evaluation_task(driver, task_running=True):
    """执行GPT-5评估提交任务"""
    try:
        # 验证配置
        if not validate_configuration():
            logger.error("配置验证失败，无法继续执行任务")
            return False

        # 打印调试信息
        print(f"开始执行GPT-5评估提交任务...")
        print(f"WebDriver类型: {type(driver).__name__}")
        if hasattr(driver, 'capabilities'):
            print(f"WebDriver能力: {driver.capabilities.get('browserName', '未知')}, 版本: {driver.capabilities.get('browserVersion', '未知')}")

        # 初始化 AI 和浏览器
        load_dotenv()
        api_key = os.getenv('GEMINI_API_KEY') or os.getenv('GOOGLE_API_KEY')
        llm = ChatGoogleGenerativeAI(
            model='gemini-2.0-flash-exp',
            api_key=SecretStr(api_key)
        )

        print("获取调试地址...")
        debugger_address = driver.capabilities['goog:chromeOptions']['debuggerAddress']
        print(f"调试地址: {debugger_address}")
        browser = Browser(config=BrowserConfig(cdp_url=f"http://{debugger_address}"))

        # 创建代理
        print("创建AI代理...")
        agent = Agent(
            task="""
            GPT-5 EVALUATION SUBMISSION TASK:

            Your goal is to navigate to https://predict.recall.network/evaluations and submit a high-quality evaluation for GPT-5.

            STEP 1: Navigate to the website
            - Go to https://predict.recall.network/evaluations
            - Wait for the page to fully load
            - Take time to read and understand the page content
            - Verify you can see the "Create an Evaluation" form

            STEP 2: Select a skill to evaluate
            - Click on the "Select Skill" dropdown menu
            - Choose one of the available skills (e.g., reasoning, mathematics, coding, language understanding, etc.)
            - Make sure the skill is selected and displayed

            STEP 3: Write an evaluation prompt
            - Click in the "Evaluation prompt" text area
            - Write a creative, challenging, and specific test question for the selected skill
            - The prompt should be:
              * Specific and testable
              * Objectively evaluable
              * Clear and unambiguous
              * Challenging for AI models
              * Between 100-1000 characters
            - Examples of good prompts:
              * For reasoning: "Given a scenario with multiple constraints, determine the optimal solution"
              * For mathematics: "Solve a multi-step word problem involving calculus and probability"
              * For coding: "Write a function that solves a specific algorithmic challenge"

            STEP 4: Write judging instructions
            - Click in the "Judging instructions" text area
            - Explain clearly how to evaluate the AI's response
            - Include:
              * What constitutes a correct answer
              * How to measure quality/success
              * Specific criteria for evaluation
              * If there's a known answer, include it
            - Be detailed and objective in your criteria

            STEP 5: Set difficulty level
            - Select the appropriate difficulty level (easy, medium, hard)
            - Consider the complexity of your evaluation prompt
            - Default is "medium" which is usually appropriate

            STEP 6: Add author notes (optional)
            - If desired, add notes about:
              * Where you got the idea
              * Whether you've tested it
              * If you think OpenAI has seen similar problems
              * Any additional context

            STEP 7: Submit the evaluation
            - Review all fields to ensure they're filled correctly
            - Click "Submit Evaluation" button
            - Wait for confirmation that the submission was successful
            - Take a screenshot of the success message

            IMPORTANT GUIDELINES:
            - Be creative and original in your evaluation prompts
            - Focus on skills that are genuinely challenging for AI
            - Ensure your prompts test real capabilities, not memorization
            - Write clear, unambiguous instructions
            - Make sure all required fields are properly filled
            - Respect the daily submission limit (5 submissions per day)
            - Act naturally and take reasonable time between actions

            ERROR HANDLING:
            - If any field shows an error, read the error message and correct it
            - If submission fails, check all required fields are filled
            - If the page doesn't load, refresh and try again
            - Take screenshots at key steps for debugging
            """,
            llm=llm,
            browser=browser
        )

        # 错误计数和最大重试次数
        error_count = 0
        max_retries = 3
        
        # 运行任务
        while task_running:
            try:
                logger.info("开始执行GPT-5评估提交任务...")
                asyncio.run(agent.run())
                # 如果成功执行，重置错误计数
                error_count = 0
                logger.info("GPT-5评估提交任务执行完成")
                # 评估提交任务通常只需要执行一次，完成后退出
                break

            except KeyboardInterrupt:
                logger.info("用户中断任务执行")
                return False

            except Exception as e:
                error_count += 1
                logger.error(f"任务执行出错 ({error_count}/{max_retries}): {str(e)}")

                if error_count >= max_retries:
                    logger.error(f"连续出错达到{max_retries}次，任务自动停止")
                    return False

                # 暂停一段时间后重试
                logger.info(f"将在5秒后重试...")
                time.sleep(5)

            # 检查任务状态
            if not task_running:
                logger.info("任务已被设置为停止状态，退出循环")
                break

        return True

    except Exception as e:
        logger.error(f"GPT-5评估提交任务执行失败: {str(e)}")
        return False

# 为了保持向后兼容性，保留原始函数名
def run_task(driver, task_running=True):
    """兼容性函数 - 调用新的评估提交任务"""
    return run_evaluation_task(driver, task_running)

# 保留旧函数名以支持现有调用
def run_mining_task(driver, task_running=True):
    """兼容性函数 - 调用新的评估提交任务"""
    return run_evaluation_task(driver, task_running)

def stop_task():
    """停止当前正在执行的任务"""
    logger.info("准备停止任务...")
    return False  # 返回False将使task_running变为False

if __name__ == "__main__":
    print("GPT-5评估提交脚本")
    print("此脚本需要通过 Chrome 管理器运行")
    print("将自动访问 https://predict.recall.network/evaluations 并提交评估")
    print("如需停止任务，请按Ctrl+C")
from langchain_google_genai import ChatGoogleGenerativeAI
from browser_use import Agent, <PERSON>rows<PERSON>, BrowserConfig
from pydantic import SecretStr
from dotenv import load_dotenv
import os
import asyncio
import logging
import time
import traceback
import random
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 尝试导入反检测配置
try:
    import sys
    import os
    # 添加当前目录到Python路径
    current_dir = os.path.dirname(os.path.abspath(__file__))
    if current_dir not in sys.path:
        sys.path.insert(0, current_dir)

    from anti_detection_config import AntiDetectionConfig, HumanBehaviorGenerator
    ANTI_DETECTION_AVAILABLE = True
    logger.info("反检测配置模块加载成功")
except ImportError as e:
    ANTI_DETECTION_AVAILABLE = False
    logger.warning(f"反检测配置模块未找到: {e}，将使用基础配置")

def get_human_delay(min_seconds=1, max_seconds=3):
    """生成人性化的随机延迟时间"""
    return random.uniform(min_seconds, max_seconds)

def get_typing_delay():
    """生成打字间隔的随机延迟（模拟真实打字速度）"""
    return random.uniform(0.05, 0.2)  # 50-200ms之间

def get_thinking_delay():
    """生成思考时间的随机延迟"""
    return random.uniform(2, 8)  # 2-8秒思考时间

def get_reading_delay():
    """生成阅读页面内容的延迟"""
    return random.uniform(3, 10)  # 3-10秒阅读时间

def validate_configuration():
    """验证必要的配置是否正确"""
    try:
        load_dotenv()
        # 尝试多个可能的API密钥环境变量名
        api_key = os.getenv('GEMINI_API_KEY') or os.getenv('GOOGLE_API_KEY')

        if not api_key:
            logger.error("未找到API密钥环境变量")
            logger.error("请在.env文件中设置GEMINI_API_KEY或GOOGLE_API_KEY")
            return False

        if api_key.startswith('your_') or len(api_key) < 10:
            logger.error("API密钥似乎无效，请检查密钥格式")
            return False

        logger.info(f"配置验证通过，使用API密钥: {api_key[:10]}...")
        return True

    except Exception as e:
        logger.error(f"配置验证失败: {str(e)}")
        return False

def run_evaluation_task(driver, task_running=True):
    """执行GPT-5评估提交任务"""
    try:
        # 验证配置
        if not validate_configuration():
            logger.error("配置验证失败，无法继续执行任务")
            return False

        # 打印调试信息
        print(f"开始执行GPT-5评估提交任务...")
        print(f"WebDriver类型: {type(driver).__name__}")
        if hasattr(driver, 'capabilities'):
            print(f"WebDriver能力: {driver.capabilities.get('browserName', '未知')}, 版本: {driver.capabilities.get('browserVersion', '未知')}")

        # 初始化 AI 和浏览器
        load_dotenv()
        api_key = os.getenv('GEMINI_API_KEY') or os.getenv('GOOGLE_API_KEY')
        llm = ChatGoogleGenerativeAI(
            model='gemini-2.0-flash-exp',
            api_key=SecretStr(api_key)
        )

        print("获取调试地址...")
        debugger_address = driver.capabilities['goog:chromeOptions']['debuggerAddress']
        print(f"调试地址: {debugger_address}")
        browser = Browser(config=BrowserConfig(cdp_url=f"http://{debugger_address}"))

        # 创建代理
        print("创建AI代理...")
        agent = Agent(
            task="""
            GPT-5 EVALUATION SUBMISSION TASK:

            Your goal is to navigate to https://predict.recall.network/evaluations and submit a high-quality evaluation for GPT-5.

            IMPORTANT: Act like a real human user to avoid detection. Follow these human-like behaviors:

            HUMAN-LIKE BEHAVIORS:
            - Take time to read and understand the page content (wait 3-10 seconds after page loads)
            - Scroll down slowly to explore the page before starting
            - Move mouse cursor naturally around the page
            - Pause and think before filling each field (2-8 seconds)
            - Type at human speed with occasional pauses
            - Sometimes click away and come back to a field
            - Read instructions carefully before proceeding

            STEP 1: Navigate to the website
            - Go to https://predict.recall.network/evaluations
            - Wait for the page to fully load (at least 3 seconds)
            - Scroll down slowly to read the page content
            - Take time to understand what the page is about (5-8 seconds)
            - Scroll back up to the form area
            - Verify you can see the "Create an Evaluation" form

            STEP 2: Select a skill to evaluate
            - Pause and think about which skill to choose (3-5 seconds)
            - Move mouse to the "Select Skill" dropdown menu
            - Click on the dropdown and review available options
            - Take time to consider the options (2-4 seconds)
            - Choose one skill (reasoning, mathematics, coding, language understanding, etc.)
            - Make sure the skill is selected and displayed
            - Wait a moment to confirm selection (1-2 seconds)

            STEP 3: Write an evaluation prompt
            - Move mouse to the "Evaluation prompt" text area
            - Click in the text area
            - Pause to think about what to write (3-6 seconds)
            - Start typing slowly and naturally
            - Write a creative, challenging, and specific test question for the selected skill
            - The prompt should be:
              * Specific and testable
              * Objectively evaluable
              * Clear and unambiguous
              * Challenging for AI models
              * Between 100-1000 characters
            - Examples of good prompts:
              * For reasoning: "Given a scenario with multiple constraints, determine the optimal solution"
              * For mathematics: "Solve a multi-step word problem involving calculus and probability"
              * For coding: "Write a function that solves a specific algorithmic challenge"

            STEP 4: Write judging instructions
            - Scroll down if needed to see the "Judging instructions" field
            - Take a moment to read the field label and understand what's needed (2-3 seconds)
            - Click in the "Judging instructions" text area
            - Pause to organize thoughts about evaluation criteria (4-7 seconds)
            - Type slowly and thoughtfully
            - Explain clearly how to evaluate the AI's response
            - Include:
              * What constitutes a correct answer
              * How to measure quality/success
              * Specific criteria for evaluation
              * If there's a known answer, include it
            - Be detailed and objective in your criteria

            STEP 5: Set difficulty level
            - Look at the difficulty level options
            - Think about the complexity of your evaluation prompt (2-3 seconds)
            - Select the appropriate difficulty level (easy, medium, hard)
            - Default is "medium" which is usually appropriate

            STEP 6: Add author notes (optional)
            - Scroll down to see the author notes field
            - Consider whether to add notes (1-2 seconds)
            - If desired, click in the field and add notes about:
              * Where you got the idea
              * Whether you've tested it
              * If you think OpenAI has seen similar problems
              * Any additional context
            - Type naturally with occasional pauses

            STEP 7: Submit the evaluation
            - Scroll to review all filled fields
            - Take time to double-check everything (5-10 seconds)
            - Read each field again to ensure quality
            - Move mouse to "Submit Evaluation" button
            - Pause before clicking (1-2 seconds)
            - Click "Submit Evaluation" button
            - Wait for confirmation that the submission was successful
            - Take a screenshot of the success message

            CONTENT CREATION GUIDELINES:
            - Be creative and original in your evaluation prompts
            - Focus on skills that are genuinely challenging for AI
            - Ensure your prompts test real capabilities, not memorization
            - Write in a natural, human style with slight imperfections
            - Use varied sentence structures and vocabulary
            - Include domain-specific knowledge that shows expertise
            - Make content feel personal and thoughtful, not generated
            - Avoid overly formal or robotic language patterns
            - Add subtle human touches like personal opinions or experiences
            - Make sure all required fields are properly filled
            - Respect the daily submission limit (5 submissions per day)

            CONTENT EXAMPLES FOR DIFFERENT SKILLS:
            - Reasoning: Real-world scenarios with ethical dilemmas or complex trade-offs
            - Mathematics: Problems that require creative problem-solving, not just calculation
            - Coding: Algorithmic challenges with practical applications
            - Language: Tasks requiring cultural understanding or nuanced interpretation
            - Science: Experiments or phenomena that require deep understanding

            ANTI-DETECTION MEASURES:
            - Vary your timing - don't be too fast or too consistent
            - Sometimes pause mid-typing as if thinking
            - Occasionally move mouse around the page naturally
            - Use different approaches each time (different skills, styles)
            - Add natural variations in typing speed and pauses
            - Sometimes scroll up and down as if reviewing
            - Act like you're carefully considering each decision
            - Don't rush through the process

            ERROR HANDLING:
            - If any field shows an error, read the error message and correct it
            - If submission fails, check all required fields are filled
            - If the page doesn't load, refresh and try again
            - Take screenshots at key steps for debugging
            - If detected as bot, wait longer between actions and be more human-like
            """,
            llm=llm,
            browser=browser
        )

        # 错误计数和最大重试次数
        error_count = 0
        max_retries = 3
        
        # 运行任务
        while task_running:
            try:
                logger.info("开始执行GPT-5评估提交任务...")
                asyncio.run(agent.run())
                # 如果成功执行，重置错误计数
                error_count = 0
                logger.info("GPT-5评估提交任务执行完成")
                # 评估提交任务通常只需要执行一次，完成后退出
                break

            except KeyboardInterrupt:
                logger.info("用户中断任务执行")
                return False

            except Exception as e:
                error_count += 1
                error_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                logger.error(f"任务执行出错 ({error_count}/{max_retries}) at {error_time}: {str(e)}")

                # 记录详细的错误堆栈信息
                logger.debug(f"错误堆栈信息:\n{traceback.format_exc()}")

                if error_count >= max_retries:
                    logger.error(f"连续出错达到{max_retries}次，任务自动停止")
                    logger.error("请检查网络连接、API密钥配置或网页是否可访问")
                    return False

                # 根据错误类型调整重试间隔
                retry_delay = min(10 * error_count, 60)  # 递增延迟，最大60秒
                logger.info(f"将在{retry_delay}秒后重试...")
                time.sleep(retry_delay)

            # 检查任务状态
            if not task_running:
                logger.info("任务已被设置为停止状态，退出循环")
                break

        return True

    except Exception as e:
        logger.error(f"GPT-5评估提交任务执行失败: {str(e)}")
        return False

# 为了保持向后兼容性，保留原始函数名
def run_task(driver, task_running=True):
    """兼容性函数 - 调用新的评估提交任务"""
    return run_evaluation_task(driver, task_running)

# 保留旧函数名以支持现有调用
def run_mining_task(driver, task_running=True):
    """兼容性函数 - 调用新的评估提交任务"""
    return run_evaluation_task(driver, task_running)

def stop_task():
    """停止当前正在执行的任务"""
    logger.info("准备停止任务...")
    return False  # 返回False将使task_running变为False

if __name__ == "__main__":
    print("GPT-5评估提交脚本")
    print("此脚本需要通过 Chrome 管理器运行")
    print("将自动访问 https://predict.recall.network/evaluations 并提交评估")
    print("如需停止任务，请按Ctrl+C")
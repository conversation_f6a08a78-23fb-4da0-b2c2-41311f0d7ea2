"""
反检测配置文件
包含各种人性化行为参数和反检测策略
"""

import random
import time

class AntiDetectionConfig:
    """反检测配置类"""
    
    # 时间延迟配置
    DELAYS = {
        'page_load_wait': (3, 8),           # 页面加载后等待时间
        'reading_time': (5, 15),            # 阅读页面内容时间
        'thinking_time': (2, 8),            # 思考时间
        'typing_pause': (0.1, 0.5),         # 打字间隔
        'field_switch': (1, 3),             # 切换字段间隔
        'review_time': (5, 12),             # 检查内容时间
        'submit_hesitation': (2, 5),        # 提交前犹豫时间
    }
    
    # 鼠标行为配置
    MOUSE_BEHAVIOR = {
        'natural_movement': True,           # 启用自然鼠标移动
        'random_clicks': True,              # 随机点击空白区域
        'hover_elements': True,             # 悬停在元素上
        'scroll_behavior': True,            # 自然滚动行为
    }
    
    # 内容生成策略
    CONTENT_STRATEGY = {
        'vary_writing_style': True,         # 变化写作风格
        'add_personal_touch': True,         # 添加个人色彩
        'include_imperfections': True,      # 包含轻微不完美
        'use_domain_knowledge': True,       # 使用领域知识
        'avoid_ai_patterns': True,          # 避免AI模式
    }
    
    # 技能选择策略
    SKILL_ROTATION = [
        'reasoning',
        'mathematics', 
        'coding',
        'language_understanding',
        'creative_writing',
        'scientific_reasoning',
        'logical_analysis',
        'problem_solving'
    ]
    
    # 内容模板（避免重复）
    EVALUATION_TEMPLATES = {
        'reasoning': [
            "Design a scenario where multiple stakeholders have conflicting interests and determine the most ethical solution.",
            "Analyze a complex real-world situation with incomplete information and provide a reasoned decision.",
            "Evaluate competing hypotheses about a social phenomenon using logical reasoning."
        ],
        'mathematics': [
            "Solve an optimization problem with real-world constraints and multiple variables.",
            "Prove a mathematical theorem using creative approaches beyond standard methods.",
            "Apply statistical reasoning to analyze a dataset with potential biases."
        ],
        'coding': [
            "Implement an algorithm that balances efficiency with code readability for a specific use case.",
            "Debug a complex system with multiple interacting components and edge cases.",
            "Design a data structure for a novel application with specific performance requirements."
        ]
    }
    
    @staticmethod
    def get_random_delay(delay_type):
        """获取指定类型的随机延迟"""
        if delay_type in AntiDetectionConfig.DELAYS:
            min_delay, max_delay = AntiDetectionConfig.DELAYS[delay_type]
            return random.uniform(min_delay, max_delay)
        return random.uniform(1, 3)  # 默认延迟
    
    @staticmethod
    def get_random_skill():
        """获取随机技能"""
        return random.choice(AntiDetectionConfig.SKILL_ROTATION)
    
    @staticmethod
    def get_evaluation_template(skill):
        """获取评估模板"""
        if skill in AntiDetectionConfig.EVALUATION_TEMPLATES:
            return random.choice(AntiDetectionConfig.EVALUATION_TEMPLATES[skill])
        return "Create a challenging test that requires deep understanding and creative problem-solving."
    
    @staticmethod
    def should_add_random_behavior():
        """是否添加随机行为"""
        return random.random() < 0.3  # 30% 概率添加随机行为
    
    @staticmethod
    def get_typing_pattern():
        """获取打字模式"""
        patterns = [
            'steady',      # 稳定打字
            'burst',       # 突发式打字
            'thoughtful',  # 思考式打字（有停顿）
            'corrective'   # 修正式打字（偶尔删除重写）
        ]
        return random.choice(patterns)

# 人性化行为指令生成器
class HumanBehaviorGenerator:
    """生成人性化行为指令"""
    
    @staticmethod
    def generate_pre_action_behavior():
        """生成动作前的行为"""
        behaviors = []
        
        if AntiDetectionConfig.should_add_random_behavior():
            behaviors.extend([
                "Move mouse cursor naturally around the page",
                "Scroll up and down slightly as if reading",
                "Pause and hover over different elements"
            ])
        
        # 添加思考时间
        thinking_time = AntiDetectionConfig.get_random_delay('thinking_time')
        behaviors.append(f"Wait {thinking_time:.1f} seconds as if thinking")
        
        return behaviors
    
    @staticmethod
    def generate_typing_behavior(content_length):
        """生成打字行为"""
        pattern = AntiDetectionConfig.get_typing_pattern()
        
        if pattern == 'thoughtful':
            return [
                "Type slowly with occasional pauses",
                "Pause mid-sentence as if thinking about word choice",
                "Vary typing speed throughout"
            ]
        elif pattern == 'corrective':
            return [
                "Type normally but occasionally delete and retype words",
                "Make minor corrections as if refining thoughts",
                "Add content gradually with revisions"
            ]
        elif pattern == 'burst':
            return [
                "Type in bursts with pauses between thoughts",
                "Write quickly then pause to review",
                "Add content in logical chunks"
            ]
        else:  # steady
            return [
                "Type at a consistent, human-like pace",
                "Maintain steady rhythm with natural variations",
                "Pause briefly between sentences"
            ]
    
    @staticmethod
    def generate_review_behavior():
        """生成检查行为"""
        return [
            "Scroll back to review previous fields",
            "Read through content as if checking for errors",
            "Move cursor to different fields as if comparing",
            "Take time to ensure everything looks correct"
        ]

# 使用示例
def get_enhanced_task_instructions():
    """获取增强的任务指令"""
    config = AntiDetectionConfig()
    behavior_gen = HumanBehaviorGenerator()
    
    # 生成动态指令
    pre_behaviors = behavior_gen.generate_pre_action_behavior()
    typing_behaviors = behavior_gen.generate_typing_behavior(500)  # 假设500字符内容
    review_behaviors = behavior_gen.generate_review_behavior()
    
    return {
        'pre_action_behaviors': pre_behaviors,
        'typing_behaviors': typing_behaviors,
        'review_behaviors': review_behaviors,
        'delays': config.DELAYS,
        'selected_skill': config.get_random_skill()
    }
